{"name": "cloudretail", "version": "1.0.0", "private": true, "workspaces": ["apps/*"], "scripts": {"api:docker:compose:down": "yarn workspace @cloudretail/api docker:compose:down", "api:docker:compose:up": "yarn workspace @cloudretail/api docker:compose:up", "api:lint": "yarn workspace @cloudretail/api lint", "api:start": "yarn workspace @cloudretail/api start", "api:typecheck": "yarn workspace @cloudretail/api typecheck", "format": "prettier --ignore-unknown --write .", "lint": "npm-run-all -p api:lint web-ui:lint", "prepare": "husky", "start": "npm-run-all -p api:start web-ui:start", "typecheck": "npm-run-all -p api:typecheck web-ui:typecheck", "web-ui:lint": "yarn workspace @cloudretail/web-ui lint", "web-ui:start": "yarn workspace @cloudretail/web-ui start", "web-ui:typecheck": "yarn workspace @cloudretail/web-ui typecheck"}, "devDependencies": {"husky": "^9.1.7", "lint-staged": "^16.1.2", "npm-run-all": "^4.1.5", "prettier": "^3.6.0"}}