FROM node:22 AS build
ARG VITE_APP_BUILD_HASH
ARG VITE_APP_BUILD_TIMESTAMP
ENV VITE_APP_BUILD_HASH=$VITE_APP_BUILD_HASH
ENV VITE_APP_BUILD_TIMESTAMP=$VITE_APP_BUILD_TIMESTAMP
WORKDIR /app
COPY . ./
RUN yarn install
RUN yarn workspace @cloudretail/web-ui build
RUN find ./apps/web-ui/dist -type f | xargs gzip -k

FROM nginx:1
COPY apps/web-ui/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/apps/web-ui/dist /usr/share/nginx/html
