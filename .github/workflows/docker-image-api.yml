name: Build Docker Image for API

env:
  IMAGE: ghcr.io/${{github.repository}}/api:${{github.ref_name}}

on:
  push:
    paths:
      - apps/api/**

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: cp yarn.lock apps/api
      - run: docker login -u ${{github.repository_owner}} -p ${{secrets.GITHUB_TOKEN}} ghcr.io
      - run: docker build -f dockerfiles/api.dockerfile -t ${{env.IMAGE}} .
      - run: docker push ${{env.IMAGE}}
