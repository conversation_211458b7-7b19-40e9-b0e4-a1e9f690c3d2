// @ts-check
import eslint from '@eslint/js'
import tseslint from 'typescript-eslint'
import safeql from '@ts-safeql/eslint-plugin/config'

export default tseslint.config(
  eslint.configs.recommended,
  ...tseslint.configs.strictTypeChecked,
  {
    languageOptions: {
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
  {
    files: ['**/*.{cjs,mjs,js,jsx}'],
    extends: [tseslint.configs.disableTypeChecked],
  },
  safeql.configs.connections({
    databaseUrl: 'postgres://postgres:postgres@localhost:5432/postgres',
    overrides: {
      types: {
        uuid: 'string',
        date: 'string',
        time: 'string',
        timetz: 'string',
        timestamp: 'string',
        timestamptz: 'string',
      },
    },
    targets: [{ wrapper: 'client.query' }],
  }),
  {
    rules: {
      '@typescript-eslint/consistent-type-definitions': ['error', 'type'],
      '@typescript-eslint/consistent-type-imports': [
        'error',
        { fixStyle: 'inline-type-imports' },
      ],
      '@typescript-eslint/no-empty-object-type': 'off',
      '@typescript-eslint/no-misused-promises': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-unnecessary-type-parameters': 'off',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],
      '@typescript-eslint/unbound-method': 'off',
      'no-duplicate-imports': 'error',
      'no-multiple-empty-lines': 'error',
      'object-shorthand': 'error',
    },
  },
)
