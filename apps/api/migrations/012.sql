-- Up Migration
create table product_units (
  id uuid default gen_random_uuid() not null primary key,
  created_at timestamp default current_timestamp not null,
  product_id uuid not null references products (id) on delete cascade,
  unit_id uuid not null references units (id) on delete cascade,
  is_base_unit boolean not null default false,
  unique(product_id, unit_id)
);

insert into product_units (product_id, unit_id, is_base_unit)
select id, base_unit_id, true
from products
where base_unit_id is not null;

alter table products drop column base_unit_id;

-- Down Migration
alter table products add column base_unit_id uuid references units;

update products 
set base_unit_id = pu.unit_id
from product_units pu
where products.id = pu.product_id and pu.is_base_unit = true;

alter table products alter column base_unit_id set not null;

drop table product_units;
