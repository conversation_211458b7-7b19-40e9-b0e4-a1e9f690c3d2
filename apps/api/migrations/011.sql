-- Up Migration
create table order_items (
  id uuid default gen_random_uuid() not null primary key,
  created_at timestamp default current_timestamp not null,
  order_id uuid not null references orders (id) on delete cascade,
  product_id uuid not null references products (id),
  quantity decimal(10, 3) not null,
  price decimal(10, 2) not null,
  total decimal(10, 2) not null
);

-- Down Migration
drop table order_items;
