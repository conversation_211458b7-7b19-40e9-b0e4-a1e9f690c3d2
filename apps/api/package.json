{"name": "@cloudretail/api", "version": "1.0.0", "private": true, "exports": "./src/exports.ts", "types": "./src/types.ts", "scripts": {"build": "tsc --outDir dist", "docker:compose:down": "docker compose down --remove-orphans --volumes", "docker:compose:up": "docker compose up -d", "lint": "DEBUG=eslint:cli-engine eslint --fix .", "migrate:make": "dotenv -c -- npx pg-migrate make", "migrate:up": "dotenv -c -- npx pg-migrate up", "migrate:down": "dotenv -c -- npx pg-migrate down", "migrate:sync": "dotenv -c -- npx pg-migrate sync", "start:server": "tsx watch --clear-screen=false src/index.ts", "start": "dotenv -c -- npx npm-run-all -s migrate:sync start:server", "typecheck": "tsc --noEmit", "typecheck:w": "tsc --noEmit --preserveWatchOutput --watch"}, "dependencies": {"@ravshansbox/pg-migrate": "^0.8.0", "@ts-safeql/sql-tag": "^0.2.1", "dotenv-cli": "^8.0.0", "find-my-way": "^9.3.0", "nodemailer": "^7.0.3", "pg": "^8.16.2", "remeda": "^2.23.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@ts-safeql/eslint-plugin": "^4.0.0", "@types/node": "^24.0.3", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "eslint": "^9.29.0", "libpg-query": "^17.5.2", "npm-run-all": "^4.1.5", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0"}}