import type findMyWay from 'find-my-way'
import { type Route } from './http-types.js'
import { parseBody } from './utils/index.js'

export const createRoute = <RequestParams, RequestBody, ResponseBody>(
  route: Route<RequestParams, RequestBody, ResponseBody>,
) => route

export const createHttpResult = <RequestBody>(
  statusCode: number,
  body?: RequestBody,
) => ({ statusCode, body })

export const registerRoute = <RequestParams, RequestBody, ResponseBody>(
  app: findMyWay.Instance<findMyWay.HTTPVersion.V1>,
  route: Route<RequestParams, RequestBody, ResponseBody>,
) => {
  app.on(
    route.endpoint.method,
    route.endpoint.path,
    async (request, response, params) => {
      let parsedBody: RequestBody | undefined = undefined
      if (route.bodySchema) {
        const rawBody = await parseBody(request)
        const body = JSON.parse(rawBody.toString()) as Record<string, unknown>
        parsedBody = route.bodySchema.parse(body)
      }
      try {
        const result = await route.handler({
          request,
          response,
          params: params as RequestParams,
          body: parsedBody as RequestBody,
        })
        response.statusCode = result.statusCode
        if (result.body) {
          response.setHeader('Content-Type', 'application/json')
          response.end(JSON.stringify(result.body))
        } else {
          response.end()
        }
      } catch (error) {
        console.error(error)
        response.statusCode = 500
        response.setHeader('Content-Type', 'application/json')
        response.end(JSON.stringify({ error: 'Internal Server Error' }))
      }
    },
  )
}
