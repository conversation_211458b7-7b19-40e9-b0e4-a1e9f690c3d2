import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow, getRows } from '../utils/index.js'

export const selectUnitsByProductId = (
  client: QueryClient,
  values: { product_id: string },
) =>
  getRows(
    client.query<{ id: string; created_at: string; product_id: string; unit_id: string; is_base_unit: boolean; unit_name: string }>(
      sql`select pu.*, u.name as unit_name from product_units pu join units u on pu.unit_id = u.id where pu.product_id = ${values.product_id}::uuid order by pu.is_base_unit desc, u.name`,
    ),
  )

export const selectProductsByUnitId = (
  client: QueryClient,
  values: { unit_id: string },
) =>
  getRows(
    client.query<{ id: string; created_at: string; product_id: string; unit_id: string; is_base_unit: boolean; product_name: string }>(
      sql`select pu.*, p.name as product_name from product_units pu join products p on pu.product_id = p.id where pu.unit_id = ${values.unit_id}::uuid order by pu.is_base_unit desc, p.name`,
    ),
  )

export const selectBaseUnitByProductId = (
  client: QueryClient,
  values: { product_id: string },
) =>
  getFirstRow(
    client.query<{ id: string; created_at: string; product_id: string; unit_id: string; is_base_unit: boolean; unit_name: string }>(
      sql`select pu.*, u.name as unit_name from product_units pu join units u on pu.unit_id = u.id where pu.product_id = ${values.product_id}::uuid and pu.is_base_unit = true`,
    ),
  )

export const insertOne = (
  client: QueryClient,
  values: {
    product_id: string
    unit_id: string
    is_base_unit?: boolean
    item_count?: number
  },
) =>
  getFirstRow(
    client.query<{
      id: string | null
      created_at: string | null
      product_id: string | null
      unit_id: string | null
      is_base_unit: boolean | null
      item_count: string | null
    }>(
      sql`insert into product_units (product_id, unit_id, is_base_unit, item_count) values (${values.product_id}::uuid, ${values.unit_id}::uuid, ${values.is_base_unit ?? false}, ${values.item_count ?? 1.0}) returning *`,
    ),
  )

export const deleteOne = (
  client: QueryClient,
  values: { product_id: string; unit_id: string },
) =>
  getFirstRow(
    client.query<{ id: string; created_at: string; product_id: string; unit_id: string; is_base_unit: boolean }>(
      sql`delete from product_units where product_id = ${values.product_id}::uuid and unit_id = ${values.unit_id}::uuid returning *`,
    ),
  )

export const updateBaseUnit = (
  client: QueryClient,
  values: { product_id: string; unit_id: string },
) =>
  client.query(
    sql`
      update product_units
      set is_base_unit = case
        when unit_id = ${values.unit_id}::uuid then true
        else false
      end
      where product_id = ${values.product_id}::uuid
    `,
  )

export const updateItemCount = (
  client: QueryClient,
  values: { product_id: string; unit_id: string; item_count: number },
) =>
  getFirstRow(
    client.query<{
      id: string | null
      created_at: string | null
      product_id: string | null
      unit_id: string | null
      is_base_unit: boolean | null
      item_count: string | null
    }>(
      sql`update product_units set item_count = ${values.item_count} where product_id = ${values.product_id}::uuid and unit_id = ${values.unit_id}::uuid returning *`,
    ),
  )
