import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow } from '../utils/index.js'

export const selectOneByUserId = (
  client: QueryClient,
  values: { user_id: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      user_id: string
    }>(sql`select * from permissions where user_id = ${values.user_id}::uuid`),
  )

export const selectOne = (
  client: QueryClient,
  values: { company_id: string; user_id: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      user_id: string
    }>(
      sql`select * from permissions where company_id = ${values.company_id}::uuid and user_id = ${values.user_id}::uuid`,
    ),
  )

export const createPermission = (
  client: QueryClient,
  values: { company_id: string; user_id: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      user_id: string
    }>(
      sql`insert into permissions (company_id, user_id) values (${values.company_id}::uuid, ${values.user_id}::uuid) returning *`,
    ),
  )
