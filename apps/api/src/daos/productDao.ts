import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow, getRows } from '../utils/index.js'

export const selectManyByCompanyId = (
  client: QueryClient,
  values: { company_id: string },
) =>
  getRows(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
      base_unit_id: string | null
      base_unit_name: string | null
    }>(
      sql`
        select
          p.*,
          pu.unit_id as base_unit_id,
          u.name as base_unit_name
        from products p
        left join product_units pu on pu.product_id = p.id and pu.is_base_unit = true
        left join units u on u.id = pu.unit_id
        where p.company_id = ${values.company_id}::uuid
      `,
    ),
  )

export const selectOneById = (client: QueryClient, values: { id: string }) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
      base_unit_id: string | null
      base_unit_name: string | null
    }>(
      sql`
        select
          p.*,
          pu.unit_id as base_unit_id,
          u.name as base_unit_name
        from products p
        left join product_units pu on pu.product_id = p.id and pu.is_base_unit = true
        left join units u on u.id = pu.unit_id
        where p.id = ${values.id}::uuid
      `,
    ),
  )

export const insertOne = (
  client: QueryClient,
  values: { company_id: string; name: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
    }>(
      sql`insert into products (company_id, name) values (${values.company_id}::uuid, ${values.name}) returning *`,
    ),
  )

export const updateOne = (
  client: QueryClient,
  values: { id: string; name: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
    }>(
      sql`update products set name = ${values.name} where id = ${values.id}::uuid returning *`,
    ),
  )
