import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow } from '../utils/index.js'

export const getToken = (client: QueryClient, values: { id: string }) =>
  getFirstRow(
    client.query<{ id: string; created_at: string; user_id: string }>(
      sql`select * from tokens where id = ${values.id}::uuid`,
    ),
  )

export const insertOne = (client: QueryClient, values: { user_id: string }) =>
  getFirstRow(
    client.query<{ id: string; created_at: string; user_id: string }>(
      sql`insert into tokens (user_id) values (${values.user_id}::uuid) returning *`,
    ),
  )

export const deleteOne = (client: QueryClient, values: { id: string }) =>
  client.query(sql`delete from tokens where id = ${values.id}::uuid`)
