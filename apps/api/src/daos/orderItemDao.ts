import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow } from '../utils/getFirstRow.js'
import { getRows } from '../utils/getRows.js'

export const selectManyByOrderId = (
  client: QueryClient,
  values: { order_id: string },
) =>
  getRows(
    client.query<{
      id: string
      created_at: string
      order_id: string
      product_id: string
      quantity: string
      price: string
      total: string
      product_name: string
    }>(
      sql`select oi.*, p.name as product_name from order_items oi join products p on oi.product_id = p.id where oi.order_id = ${values.order_id}::uuid`,
    ),
  )

export const insertOne = (
  client: QueryClient,
  values: {
    order_id: string
    product_id: string
    quantity: number
    price: number
    total: number
  },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      order_id: string
      product_id: string
      quantity: string
      price: string
      total: string
    }>(
      sql`insert into order_items (order_id, product_id, quantity, price, total) values (${values.order_id}::uuid, ${values.product_id}::uuid, ${values.quantity}, ${values.price}, ${values.total}) returning *`,
    ),
  )

export const updateOne = (
  client: QueryClient,
  values: {
    id: string
    product_id: string
    quantity: number
    price: number
    total: number
  },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      order_id: string
      product_id: string
      quantity: string
      price: string
      total: string
    }>(
      sql`update order_items set product_id = ${values.product_id}::uuid, quantity = ${values.quantity}, price = ${values.price}, total = ${values.total} where id = ${values.id}::uuid returning *`,
    ),
  )

export const deleteOne = (client: QueryClient, values: { id: string }) =>
  client.query(sql`delete from order_items where id = ${values.id}::uuid`)
