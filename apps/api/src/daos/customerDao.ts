import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow, getRows } from '../utils/index.js'

export const selectManyByCompanyId = (
  client: QueryClient,
  values: { company_id: string },
) =>
  getRows(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
    }>(
      sql`select * from customers where company_id = ${values.company_id}::uuid`,
    ),
  )

export const selectOneById = (client: QueryClient, values: { id: string }) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
    }>(sql`select * from customers where id = ${values.id}::uuid`),
  )

// Alias for backwards compatibility
export const selectOne = selectOneById

export const insertOne = (
  client: QueryClient,
  values: { company_id: string; name: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
    }>(
      sql`insert into customers (company_id, name) values (${values.company_id}::uuid, ${values.name}) returning *`,
    ),
  )

export const updateOne = (
  client: QueryClient,
  values: { id: string; name: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
    }>(
      sql`update customers set name = ${values.name} where id = ${values.id}::uuid returning *`,
    ),
  )

export const deleteOne = (client: QueryClient, values: { id: string }) =>
  client.query(sql`delete from customers where id = ${values.id}::uuid`)
