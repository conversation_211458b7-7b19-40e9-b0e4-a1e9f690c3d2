import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow, getRows } from '../utils/index.js'

export const getUser = (client: QueryClient, values: { id: string }) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      is_active: boolean
      email: string
      password: string
    }>(sql`select * from users where id = ${values.id}::uuid`),
  )

export const selectOneByEmail = (
  client: QueryClient,
  values: { email: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      is_active: boolean
      email: string
      password: string
    }>(sql`select * from users where email = ${values.email}`),
  )

export const selectManyByCompanyId = (
  client: QueryClient,
  values: { company_id: string },
) =>
  getRows(
    client.query<{ id: string; is_active: boolean; email: string }>(
      sql`select u.id, u.is_active, u.email from users u join permissions p on p.user_id = u.id where p.company_id = ${values.company_id}::uuid`,
    ),
  )

export const insertOne = (
  client: QueryClient,
  values: { is_active: boolean; email: string; password: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      is_active: boolean
      email: string
      password: string
    }>(
      sql`insert into users (is_active, email, password) values (${values.is_active}, ${values.email}, ${values.password}) returning *`,
    ),
  )

export const activateOne = (client: QueryClient, values: { id: string }) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      is_active: boolean
      email: string
      password: string
    }>(
      sql`update users set is_active = true where id = ${values.id}::uuid returning *`,
    ),
  )
