import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow, getRows } from '../utils/index.js'

export const selectManyByUserId = (
  client: QueryClient,
  values: { user_id: string },
) =>
  getRows(
    client.query<{ id: string; created_at: string; name: string }>(
      sql`select c.* from companies c join permissions p on p.company_id = c.id where p.user_id = ${values.user_id}::uuid`,
    ),
  )

export const selectOneById = (client: QueryClient, values: { id: string }) =>
  getFirstRow(
    client.query<{ id: string; created_at: string; name: string }>(
      sql`select * from companies where id = ${values.id}::uuid`,
    ),
  )

export const insertOne = (client: QueryClient, values: { name: string }) =>
  getFirstRow(
    client.query<{ id: string; created_at: string; name: string }>(
      sql`insert into companies (name) values (${values.name}) returning *`,
    ),
  )

export const updateOne = (
  client: QueryClient,
  values: { id: string; name: string },
) =>
  getFirstRow(
    client.query<{ id: string; created_at: string; name: string }>(
      sql`update companies set name = ${values.name} where id = ${values.id}::uuid returning *`,
    ),
  )

export const deleteOne = (client: QueryClient, values: { id: string }) =>
  getFirstRow(
    client.query<{ id: string; created_at: string; name: string }>(
      sql`delete from companies where id = ${values.id}::uuid returning *`,
    ),
  )
