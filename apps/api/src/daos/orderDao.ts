import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow, getRows } from '../utils/index.js'

export const selectManyByCompanyId = (
  client: QueryClient,
  values: { company_id: string },
) =>
  getRows(
    client.query<{
      id: string
      created_at: string
      company_id: string
      customer_id: string
      total_amount: string
      customer_name: string
    }>(
      sql`select o.*, c.name as customer_name from orders o join customers c on c.id = o.customer_id where o.company_id = ${values.company_id}::uuid`,
    ),
  )

export const selectOneById = (client: QueryClient, values: { id: string }) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      customer_id: string
      total_amount: string
      customer_name: string
    }>(
      sql`select o.*, c.name as customer_name from orders o join customers c on c.id = o.customer_id where o.id = ${values.id}::uuid`,
    ),
  )

export const insertOne = (
  client: QueryClient,
  values: { company_id: string; customer_id: string; total_amount: number },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      customer_id: string
      total_amount: string
    }>(
      sql`insert into orders (company_id, customer_id, total_amount) values (${values.company_id}::uuid, ${values.customer_id}::uuid, ${values.total_amount}) returning *`,
    ),
  )

export const updateOne = (
  client: QueryClient,
  values: { id: string; customer_id: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      customer_id: string
      total_amount: string
    }>(
      sql`update orders set customer_id = ${values.customer_id}::uuid where id = ${values.id}::uuid returning *`,
    ),
  )

export const deleteOne = (client: QueryClient, values: { id: string }) =>
  client.query(sql`delete from orders where id = ${values.id}::uuid`)
