import { type IncomingMessage, type ServerResponse } from 'node:http'
import { type ZodSchema } from 'zod'

export type Method = 'GET' | 'POST' | 'PUT' | 'DELETE'

export type Endpoint<_RequestParams> = { method: Method; path: string }

export type Context<RequestParams, RequestBody> = {
  params: RequestParams
  body: RequestBody
}

export type ServerContext<RequestParams, RequestBody> = Context<
  RequestParams,
  RequestBody
> & { request: IncomingMessage; response: ServerResponse }

type OptionalProp<T, key extends string> = unknown extends T
  ? {}
  : Record<key, T>

export type ClientContext<RequestParams, RequestBody> = OptionalProp<
  RequestParams,
  'params'
> &
  OptionalProp<RequestBody, 'body'>

export type HttpResult<ResponseBody> = {
  statusCode: number
  body: ResponseBody
}

export type Handler<RequestParams, RequestBody, ResponseBody> = (
  context: ServerContext<RequestParams, RequestBody>,
) => HttpResult<ResponseBody> | Promise<HttpResult<ResponseBody>>

export type Route<RequestParams, RequestBody, ResponseBody> = {
  endpoint: Endpoint<RequestParams>
  bodySchema?: ZodSchema<RequestBody>
  handler: Handler<RequestParams, RequestBody, ResponseBody>
}

export type Fetcher<R> =
  R extends Route<infer RequestParams, infer RequestBody, infer ResponseBody>
    ? (
        context: ClientContext<RequestParams, RequestBody>,
      ) => Promise<ResponseBody>
    : never
