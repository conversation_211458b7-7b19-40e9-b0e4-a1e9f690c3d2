import { companyDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { parseAuth } from '../../utils/index.js'

export const getCompanies = createRoute({
  endpoint: endpoints.getCompanies,
  handler: async ({ request }) => {
    const { user } = await parseAuth(request)
    const companies = await companyDao.selectManyByUserId(pool, {
      user_id: user.id,
    })
    return createHttpResult(200, companies)
  },
})

export type GetCompaniesFetcher = Fetcher<typeof getCompanies>
