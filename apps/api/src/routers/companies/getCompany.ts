import { companyDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'

export const getCompany = createRoute({
  endpoint: endpoints.getCompany,
  handler: async ({ params }) => {
    const company = await companyDao.selectOneById(pool, { id: params.id })
    return createHttpResult(200, company)
  },
})

export type GetCompanyFetcher = Fetcher<typeof getCompany>
