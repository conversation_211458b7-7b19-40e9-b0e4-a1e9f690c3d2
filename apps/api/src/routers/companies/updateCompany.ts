import { z } from 'zod'
import { companyDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const updateCompany = createRoute({
  endpoint: endpoints.updateCompany,
  bodySchema: z.object({ name: z.string() }),
  handler: async ({ request, body, params }) => {
    const { user } = await parseAuth(request)
    await assertAccess(params.id, user.id, pool)
    const company = await companyDao.updateOne(pool, {
      id: params.id,
      name: body.name,
    })
    return createHttpResult(200, company)
  },
})

export type UpdateCompanyFetcher = Fetcher<typeof updateCompany>
