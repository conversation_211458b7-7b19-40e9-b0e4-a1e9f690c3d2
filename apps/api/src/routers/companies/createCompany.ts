import { z } from 'zod'
import { companyDao, permissionDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { parseAuth } from '../../utils/index.js'

export const createCompany = createRoute({
  endpoint: endpoints.createCompany,
  bodySchema: z.object({ name: z.string() }),
  handler: async ({ request, body }) => {
    const { user } = await parseAuth(request)
    const company = await companyDao.insertOne(pool, body)
    await permissionDao.createPermission(pool, {
      company_id: company.id,
      user_id: user.id,
    })
    return createHttpResult(201, company)
  },
})

export type CreateCompanyFetcher = Fetcher<typeof createCompany>
