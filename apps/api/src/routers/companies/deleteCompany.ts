import { z } from 'zod'
import { companyDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const deleteCompany = createRoute({
  endpoint: endpoints.deleteCompany,
  bodySchema: z.object({ id: z.string().uuid() }),
  handler: async ({ request, body }) => {
    const { user } = await parseAuth(request)
    await assertAccess(body.id, user.id, pool)
    await companyDao.deleteOne(pool, { id: body.id })
    return createHttpResult(204)
  },
})

export type DeleteCompanyFetcher = Fetcher<typeof deleteCompany>
