import { customerDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const deleteCustomer = createRoute({
  endpoint: endpoints.deleteCustomer,
  handler: async ({ request, params }) => {
    const { user } = await parseAuth(request)
    const customer = await customerDao.selectOne(pool, { id: params.id })
    await assertAccess(customer.company_id, user.id, pool)
    await customerDao.deleteOne(pool, { id: params.id })
    return createHttpResult(204)
  },
})

export type DeleteCustomerFetcher = Fetcher<typeof deleteCustomer>
