import { customerDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'

export const getCustomer = createRoute({
  endpoint: endpoints.getCustomer,
  handler: async ({ params }) => {
    const customer = await customerDao.selectOne(pool, { id: params.id })
    return createHttpResult(200, customer)
  },
})

export type GetCustomerFetcher = Fetcher<typeof getCustomer>
