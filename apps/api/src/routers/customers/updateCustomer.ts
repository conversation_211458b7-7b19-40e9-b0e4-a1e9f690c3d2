import { z } from 'zod'
import { customerDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const updateCustomer = createRoute({
  endpoint: endpoints.updateCustomer,
  bodySchema: z.object({ name: z.string() }),
  handler: async ({ request, body, params }) => {
    const { user } = await parseAuth(request)
    const customer = await customerDao.selectOne(pool, { id: params.id })
    await assertAccess(customer.company_id, user.id, pool)
    const updatedCustomer = await customerDao.updateOne(pool, {
      id: params.id,
      ...body,
    })
    return createHttpResult(200, updatedCustomer)
  },
})

export type UpdateCustomerFetcher = Fetcher<typeof updateCustomer>
