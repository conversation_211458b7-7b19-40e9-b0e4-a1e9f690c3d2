import * as remeda from 'remeda'
import { z } from 'zod'
import { tokenDao, userDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { hashPassword } from '../../utils/index.js'

export const createToken = createRoute({
  endpoint: endpoints.createToken,
  bodySchema: z.object({ email: z.string(), password: z.string() }),
  handler: async ({ body }) => {
    const user = await userDao.selectOneByEmail(pool, { email: body.email })
    const hashedPassword = hashPassword(body.password)
    if (hashedPassword !== user.password) {
      throw new Error('Wrong password')
    }
    const token = await tokenDao.insertOne(pool, { user_id: user.id })
    return createHttpResult(200, {
      ...token,
      user: remeda.omit(user, ['password']),
    })
  },
})

export type CreateTokenFetcher = Fetcher<typeof createToken>
