import * as remeda from 'remeda'
import { tokenDao, userDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'

export const getToken = createRoute({
  endpoint: endpoints.getToken,
  handler: async ({ params }) => {
    try {
      const token = await tokenDao.getToken(pool, { id: params.id })
      const user = await userDao.getUser(pool, { id: token.user_id })
      return createHttpResult(200, {
        ...token,
        user: remeda.omit(user, ['password']),
      })
    } catch (_error) {
      throw new Error('Unauthorized')
    }
  },
})

export type GetTokenFetcher = Fetcher<typeof getToken>
