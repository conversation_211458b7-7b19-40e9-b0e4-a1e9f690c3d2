import { tokenDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'

export const deleteToken = createRoute({
  endpoint: endpoints.deleteToken,
  handler: async ({ params }) => {
    await tokenDao.deleteOne(pool, { id: params.id })
    return createHttpResult(204)
  },
})

export type DeleteTokenFetcher = Fetcher<typeof deleteToken>
