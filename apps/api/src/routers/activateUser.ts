import assert from 'node:assert'
import { userDao } from '../daos/index.js'
import { endpoints } from '../endpoints.js'
import { createHttpResult, createRoute } from '../http.js'
import { type Fetcher } from '../http-types.js'
import { pool } from '../pool.js'

export const activateUser = createRoute({
  endpoint: endpoints.activateUser,
  handler: async ({ response, params: { id } }) => {
    const user = await userDao.getUser(pool, { id })
    assert.ok(user, 'user not found')
    assert.ok(!user.is_active, 'user already activated')
    await userDao.activateOne(pool, { id })
    response.end('activated')
    return createHttpResult(200)
  },
})

export type ActivateUserFetcher = Fetcher<typeof activateUser>
