import { z } from 'zod'
import { productDao, productUnitDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const createProduct = createRoute({
  endpoint: endpoints.createProduct,
  bodySchema: z.object({
    company_id: z.string().uuid(),
    name: z.string(),
    base_unit_id: z.string().uuid(),
  }),
  handler: async ({ request, body }) => {
    const { user } = await parseAuth(request)
    await assertAccess(body.company_id, user.id, pool)

    // Create the product first
    const product = await productDao.insertOne(pool, {
      company_id: body.company_id,
      name: body.name,
    })

    // Then create the product-unit relationship with the base unit
    await productUnitDao.insertOne(pool, {
      product_id: product.id,
      unit_id: body.base_unit_id,
      is_base_unit: true,
    })

    return createHttpResult(201, product)
  },
})

export type CreateProductFetcher = Fetcher<typeof createProduct>
