import { productDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'

export const getProduct = createRoute({
  endpoint: endpoints.getProduct,
  handler: async ({ params }) => {
    const product = await productDao.selectOneById(pool, { id: params.id })
    return createHttpResult(200, product)
  },
})

export type GetProductFetcher = Fetcher<typeof getProduct>
