import { z } from 'zod'
import { productDao, productUnitDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const updateProductUnitCount = createRoute({
  endpoint: endpoints.updateProductUnitCount,
  bodySchema: z.object({
    item_count: z.number().positive(),
  }),
  handler: async ({ request, body, params }) => {
    const { user } = await parseAuth(request)
    const product = await productDao.selectOneById(pool, { id: params.id })
    await assertAccess(product.company_id, user.id, pool)
    
    const updatedProductUnit = await productUnitDao.updateItemCount(pool, {
      product_id: params.id,
      unit_id: params.unit_id,
      item_count: body.item_count,
    })
    
    return createHttpResult(200, updatedProductUnit)
  },
})

export type UpdateProductUnitCountFetcher = Fetcher<typeof updateProductUnitCount>
