import { z } from 'zod'
import { productDao, productUnitDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const addProductUnit = createRoute({
  endpoint: endpoints.addProductUnit,
  bodySchema: z.object({
    unit_id: z.string().uuid(),
    is_base_unit: z.boolean().optional(),
    item_count: z.number().positive().optional(),
  }),
  handler: async ({ request, body, params }) => {
    const { user } = await parseAuth(request)
    const product = await productDao.selectOneById(pool, { id: params.id })
    await assertAccess(product.company_id, user.id, pool)

    const productUnit = await productUnitDao.insertOne(pool, {
      product_id: params.id,
      unit_id: body.unit_id,
      is_base_unit: body.is_base_unit ?? false,
      item_count: body.item_count ?? 1.0,
    })

    return createHttpResult(201, productUnit)
  },
})

export type AddProductUnitFetcher = Fetcher<typeof addProductUnit>
