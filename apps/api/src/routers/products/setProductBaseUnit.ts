import { z } from 'zod'
import { productDao, productUnitDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const setProductBaseUnit = createRoute({
  endpoint: endpoints.setProductBaseUnit,
  bodySchema: z.object({
    unit_id: z.string().uuid(),
  }),
  handler: async ({ request, body, params }) => {
    const { user } = await parseAuth(request)
    const product = await productDao.selectOneById(pool, { id: params.id })
    await assertAccess(product.company_id, user.id, pool)
    
    await productUnitDao.updateBaseUnit(pool, {
      product_id: params.id,
      unit_id: body.unit_id,
    })
    
    return createHttpResult(200, { success: true })
  },
})

export type SetProductBaseUnitFetcher = Fetcher<typeof setProductBaseUnit>
