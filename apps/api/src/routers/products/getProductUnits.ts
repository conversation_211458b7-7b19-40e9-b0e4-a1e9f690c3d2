import { productUnitDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'

export const getProductUnits = createRoute({
  endpoint: endpoints.getProductUnits,
  handler: async ({ params }) => {
    const units = await productUnitDao.selectUnitsByProductId(pool, { product_id: params.id })
    return createHttpResult(200, units)
  },
})

export type GetProductUnitsFetcher = Fetcher<typeof getProductUnits>
