import { z } from 'zod'
import { orderDao, orderItemDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import {
  assertAccess,
  parseAuth,
  runInTransaction,
  mapAsyncSequential,
} from '../../utils/index.js'

export const createOrder = createRoute({
  endpoint: endpoints.createOrder,
  bodySchema: z.object({
    company_id: z.string().uuid(),
    customer_id: z.string().uuid(),
    items: z.array(
      z.object({
        product_id: z.string().uuid(),
        quantity: z.number(),
        price: z.number(),
        total: z.number(),
      }),
    ),
  }),
  handler: async ({ request, body }) => {
    const { user } = await parseAuth(request)
    await assertAccess(body.company_id, user.id, pool)
    return runInTransaction(async client => {
      const total_amount = body.items.reduce((sum, item) => sum + item.total, 0)
      const order = await orderDao.insertOne(client, {
        company_id: body.company_id,
        customer_id: body.customer_id,
        total_amount,
      })
      await mapAsyncSequential(body.items, item =>
        orderItemDao.insertOne(client, {
          order_id: order.id,
          product_id: item.product_id,
          quantity: item.quantity,
          price: item.price,
          total: item.total,
        }),
      )
      const items = await orderItemDao.selectManyByOrderId(client, {
        order_id: order.id,
      })
      return createHttpResult(201, { ...order, items })
    })
  },
})

export type CreateOrderFetcher = Fetcher<typeof createOrder>
