import { orderDao, orderItemDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const getOrder = createRoute({
  endpoint: endpoints.getOrder,
  handler: async ({ request, params }) => {
    const { user } = await parseAuth(request)
    const order = await orderDao.selectOneById(pool, { id: params.id })
    await assertAccess(order.company_id, user.id, pool)
    const items = await orderItemDao.selectManyByOrderId(pool, {
      order_id: order.id,
    })
    return createHttpResult(200, { ...order, items })
  },
})

export type GetOrderFetcher = Fetcher<typeof getOrder>
