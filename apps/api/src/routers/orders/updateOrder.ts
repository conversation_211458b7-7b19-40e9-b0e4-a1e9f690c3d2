import { z } from 'zod'
import { orderDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const updateOrder = createRoute({
  endpoint: endpoints.updateOrder,
  bodySchema: z.object({ customer_id: z.string().uuid() }),
  handler: async ({ request, body, params }) => {
    const { user } = await parseAuth(request)
    const order = await orderDao.selectOneById(pool, { id: params.id })
    await assertAccess(order.company_id, user.id, pool)
    const updatedOrder = await orderDao.updateOne(pool, {
      id: params.id,
      customer_id: body.customer_id,
    })
    return createHttpResult(200, updatedOrder)
  },
})

export type UpdateOrderFetcher = Fetcher<typeof updateOrder>
