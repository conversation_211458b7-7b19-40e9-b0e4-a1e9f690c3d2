import { z } from 'zod'
import { orderDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth, parseQueryParams } from '../../utils/index.js'

const querySchema = z.object({ company_id: z.string().uuid() })

export const getOrders = createRoute({
  endpoint: endpoints.getOrders,
  handler: async ({ request }) => {
    const { user } = await parseAuth(request)
    const query = parseQueryParams(request, querySchema)
    await assertAccess(query.company_id, user.id, pool)
    const orders = await orderDao.selectManyByCompanyId(pool, query)
    return createHttpResult(200, orders)
  },
})

export type GetOrdersFetcher = Fetcher<typeof getOrders>
