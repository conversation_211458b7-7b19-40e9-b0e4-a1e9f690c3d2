import { orderDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const deleteOrder = createRoute({
  endpoint: endpoints.deleteOrder,
  handler: async ({ request, params }) => {
    const { user } = await parseAuth(request)
    const order = await orderDao.selectOneById(pool, { id: params.id })
    await assertAccess(order.company_id, user.id, pool)
    await orderDao.deleteOne(pool, { id: params.id })
    return createHttpResult(204)
  },
})

export type DeleteOrderFetcher = Fetcher<typeof deleteOrder>
