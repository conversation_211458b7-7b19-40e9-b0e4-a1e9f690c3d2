import { z } from 'zod'
import { permissionDao, userDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const authorizeUser = createRoute({
  endpoint: endpoints.updateUserAuthorize,
  bodySchema: z.object({ company_id: z.string().uuid(), email: z.string() }),
  handler: async ({ request, body }) => {
    const { user } = await parseAuth(request)
    await assertAccess(body.company_id, user.id, pool)
    const targetUser = await userDao.selectOneByEmail(pool, {
      email: body.email,
    })
    const permission = await permissionDao.createPermission(pool, {
      company_id: body.company_id,
      user_id: targetUser.id,
    })
    return createHttpResult(200, permission)
  },
})

export type AuthorizeUserFetcher = Fetcher<typeof authorizeUser>
