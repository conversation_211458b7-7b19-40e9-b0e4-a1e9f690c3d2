import nodemailer from 'nodemailer'
import { z } from 'zod'
import {
  MAIL_HOST,
  MAIL_PASSWORD,
  MAIL_PORT,
  MAIL_USER,
  VERIFICATION_URL_PREFIX,
} from '../../constants.js'
import { userDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { hashPassword } from '../../utils/index.js'

const transporter = nodemailer.createTransport({
  host: MAIL_HOST,
  port: Number(MAIL_PORT),
  secure: true,
  auth: { user: MAIL_USER, pass: MAIL_PASSWORD },
})

export const registerUser = createRoute({
  endpoint: endpoints.registerUser,
  bodySchema: z.object({ email: z.string().email(), password: z.string() }),
  handler: async ({ body }) => {
    const user = await userDao.insertOne(pool, {
      email: body.email,
      password: hashPassword(body.password),
      is_active: false,
    })
    await transporter.sendMail({
      from: MAIL_USER,
      to: body.email,
      subject: 'Confirmation',
      html: `<a href="${VERIFICATION_URL_PREFIX}/activate/${user.id}">Please, confirm your registration</a>`,
    })
    return createHttpResult(201, user)
  },
})

export type RegisterUserFetcher = Fetcher<typeof registerUser>
