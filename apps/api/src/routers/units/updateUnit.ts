import { z } from 'zod'
import { unitDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const updateUnit = createRoute({
  endpoint: endpoints.updateUnit,
  bodySchema: z.object({ name: z.string() }),
  handler: async ({ request, params, body }) => {
    const { user } = await parseAuth(request)
    const unit = await unitDao.selectOneById(pool, { id: params.id })
    await assertAccess(unit.company_id, user.id, pool)
    const updatedUnit = await unitDao.updateOne(pool, {
      id: params.id,
      name: body.name,
    })
    return createHttpResult(200, updatedUnit)
  },
})

export type UpdateUnitFetcher = Fetcher<typeof updateUnit>
