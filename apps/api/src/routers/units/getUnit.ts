import { unitDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { parseAuth } from '../../utils/index.js'

export const getUnit = createRoute({
  endpoint: endpoints.getUnit,
  handler: async ({ request, params }) => {
    await parseAuth(request)
    const unit = await unitDao.selectOneById(pool, { id: params.id })
    return createHttpResult(200, unit)
  },
})

export type GetUnitFetcher = Fetcher<typeof getUnit>
