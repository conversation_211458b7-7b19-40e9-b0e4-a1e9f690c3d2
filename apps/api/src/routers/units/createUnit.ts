import { z } from 'zod'
import { unitDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const createUnit = createRoute({
  endpoint: endpoints.createUnit,
  bodySchema: z.object({ company_id: z.string().uuid(), name: z.string() }),
  handler: async ({ request, body }) => {
    const { user } = await parseAuth(request)
    await assertAccess(body.company_id, user.id, pool)
    const unit = await unitDao.createUnit(pool, body)
    return createHttpResult(201, unit)
  },
})

export type CreateUnitFetcher = Fetcher<typeof createUnit>
