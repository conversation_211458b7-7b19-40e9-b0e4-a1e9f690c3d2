import type { PoolClient } from 'pg'
import { pool } from '../pool.js'

export const runInTransaction = async <T>(
  fn: (client: PoolClient) => Promise<T>,
) => {
  const client = await pool.connect()
  try {
    await client.query('begin')
    const result = await fn(client)
    await client.query('commit')
    return result
  } catch (error) {
    await client.query('rollback')
    throw error
  } finally {
    client.release()
  }
}
