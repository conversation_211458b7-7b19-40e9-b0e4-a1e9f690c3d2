import assert from 'node:assert'

export function getFirst<T>(promise: Promise<T[]>, strict?: true): Promise<T>
export function getFirst<T>(
  promise: Promise<T[]>,
  strict: false,
): Promise<T | undefined>
export async function getFirst<T>(promise: Promise<T[]>, strict = true) {
  const rows = await promise
  if (strict) {
    assert.notEqual(rows.length, 0, 'Array is empty')
    assert.ok(rows.length === 1, 'Array has more than one element')
  }
  return rows[0]
}
