import assert from 'node:assert'
import type http from 'node:http'
import { tokenDao, userDao } from '../daos/index.js'
import { pool } from '../pool.js'

const parseAccessTokenFromHeader = (authorization: string) => {
  const result = /^Bearer (.+)$/.exec(authorization)
  return result === null ? null : result[1]
}

export const parseAuth = async (request: http.IncomingMessage) => {
  const { authorization } = request.headers
  assert.ok(authorization, new Error('UNAUTHORIZED'))
  const tokenId = parseAccessTokenFromHeader(authorization)
  assert.ok(tokenId, new Error('UNAUTHORIZED'))
  const token = await tokenDao.getToken(pool, { id: tokenId })
  const user = await userDao.getUser(pool, { id: token.user_id })
  return { token, user }
}
