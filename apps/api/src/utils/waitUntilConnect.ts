import { Client } from 'pg'
import { DATABASE_URL } from '../constants.js'

const MAX_ATTEMPTS = 10
const WAIT_DELAY_SECONDS = 1

export const waitUntilConnect = async () => {
  const client = new Client({ connectionString: DATABASE_URL })
  for (let attempt = 0; attempt < MAX_ATTEMPTS; attempt++) {
    try {
      console.info('Trying to connect to database...')
      await client.connect()
      console.info('Connected to database.')
      await client.end()
      return
    } catch (_error) {
      console.info(
        `Connection failed, waiting for ${String(WAIT_DELAY_SECONDS)} seconds...`,
      )
      await new Promise(resolve =>
        setTimeout(resolve, WAIT_DELAY_SECONDS * 1000),
      )
    }
  }
  console.info(`Tried ${String(MAX_ATTEMPTS)} times, giving up.`)
}
