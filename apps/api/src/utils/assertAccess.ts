import assert from 'node:assert'
import { type Client, type Pool, type PoolClient } from 'pg'
import { permissionDao } from '../daos/index.js'

export const assertAccess = async (
  company_id: string,
  user_id: string,
  client: Pool | PoolClient | Client,
) => {
  const permission = await permissionDao.selectOne(client, {
    company_id,
    user_id,
  })
  assert.ok(permission, new Error('FORBIDDEN'))
}
