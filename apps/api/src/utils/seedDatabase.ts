import { hashPassword } from './index.js'
import { ADMIN_EMAIL, ADMIN_PASSWORD } from '../constants.js'
import { userDao } from '../daos/index.js'
import { pool } from '../pool.js'

export async function seedDatabase() {
  try {
    await userDao.selectOneByEmail(pool, { email: ADMIN_EMAIL })
    // Admin already exists, do nothing
  } catch {
    // Admin doesn't exist, create it
    await userDao.insertOne(pool, {
      is_active: true,
      email: ADMIN_EMAIL,
      password: hashPassword(ADMIN_PASSWORD),
    })
  }
}
