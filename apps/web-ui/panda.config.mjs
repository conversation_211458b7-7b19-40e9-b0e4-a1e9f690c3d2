import { defineConfig, defineGlobalStyles } from '@pandacss/dev'

export default defineConfig({
  eject: true,
  globalCss: defineGlobalStyles({
    '*': { boxSizing: 'inherit' },
    html: { boxSizing: 'border-box', height: '100vh' },
    body: { height: '100%', margin: '0', padding: '8px' },
    '#root': { height: '100%' },
  }),
  include: ['./src/**/*.{js,jsx,ts,tsx}'],
  jsxFramework: 'react',
  outdir: 'src/styled-system',
  preflight: false,
  presets: [],
  strictPropertyValues: true,
  strictTokens: true,
  theme: { tokens: { colors: {} } },
  utilities: { color: { values: 'colors' } },
  watch: true,
})
