import { configureStore } from '@reduxjs/toolkit'
import {
  useDispatch,
  useSelector,
  type UseDispatch,
  type UseSelector,
} from 'react-redux'
import { authSlice } from './slices/authSlice'

export const store = configureStore({ reducer: { auth: authSlice.reducer } })

export type RootState = ReturnType<typeof store.getState>
export const useAppSelector: UseSelector<RootState> = useSelector
type AppDispatch = typeof store.dispatch
export const useAppDispatch: UseDispatch<AppDispatch> = useDispatch
