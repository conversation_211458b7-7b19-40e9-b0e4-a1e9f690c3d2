import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

type AuthState = {
  isRestoreTriggered: boolean
  isRestoring: boolean
  isAuthenticating: boolean
  isAuthenticated: boolean
  tokenId: string | null
  email: string | null
}

const initialState: AuthState = {
  isRestoreTriggered: false,
  isRestoring: false,
  isAuthenticating: false,
  isAuthenticated: false,
  tokenId: null,
  email: null,
}

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setIsRestoreTriggered: state => {
      state.isRestoreTriggered = true
    },
    setIsRestoring: (state, { payload }: PayloadAction<boolean>) => {
      state.isRestoring = payload
    },
    setUserInfo: (
      state,
      { payload }: PayloadAction<Pick<AuthState, 'tokenId' | 'email'>>,
    ) => {
      state.isAuthenticating = false
      state.isAuthenticated = true
      state.tokenId = payload.tokenId
      state.email = payload.email
    },
    reset: () => ({ ...initialState, isRestoreTriggered: true }),
  },
})
