import { type Endpoint, type Method } from '@cloudretail/api'
import { compile } from 'path-to-regexp'
import { TOKEN_KEY } from './constants'

const fetchJson = async <ResponseBody>(
  method: Method,
  url: string,
  body?: Record<string, unknown>,
) => {
  const headers: Record<string, string> = { 'Content-Type': 'application/json' }
  const token = window.localStorage.getItem(TOKEN_KEY)
  if (token) headers.Authorization = `Bearer ${token}`
  const response = await fetch(url, {
    method,
    headers,
    body: method !== 'GET' && body ? JSON.stringify(body) : undefined,
  })
  if (response.ok) {
    const contentType = response.headers.get('Content-Type')
    if (contentType !== 'application/json') return null
    return response.json() as Promise<ResponseBody>
  }
  throw new Error(response.statusText)
}

export const createFetcher = <Fetcher>(endpoint: Endpoint<unknown>) => {
  const getUrl = compile(endpoint.path)
  const fetcher = ({
    params = {},
    body,
    query,
  }: {
    params?: Record<string, string>
    body?: Record<string, unknown>
    query?: Record<string, string>
  }) => {
    let url = `/api${getUrl(params)}`
    if (query && Object.keys(query).length > 0)
      url += `?${new URLSearchParams(query).toString()}`
    return fetchJson(endpoint.method, url, body)
  }
  return fetcher as Fetcher
}
