import { createBrowserRouter } from 'react-router'
import { paths } from './constants'
import { Dashboard } from './pages/Dashboard'
import { EntryLayout } from './pages/EntryLayout'
import { Login } from './pages/Login'
import { Register } from './pages/Register'
import authorizedUsersRoute from './pages/authorizedUsers/route'
import companiesRoute from './pages/companies/route'
import customersRoute from './pages/customers/route'
import ordersRoute from './pages/orders/route'
import productsRoute from './pages/products/route'
import unitsRoute from './pages/units/route'

export const router = createBrowserRouter([
  {
    path: paths.login,
    Component: EntryLayout,
    children: [{ path: '', Component: Login }],
  },
  {
    path: paths.register,
    Component: EntryLayout,
    children: [{ path: '', Component: Register }],
  },
  {
    path: '',
    Component: Dashboard,
    children: [
      authorizedUsersRoute,
      companiesRoute,
      customersRoute,
      ordersRoute,
      productsRoute,
      unitsRoute,
    ],
  },
])
