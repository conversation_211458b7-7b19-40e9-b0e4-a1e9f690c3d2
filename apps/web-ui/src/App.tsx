import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { type FC } from 'react'
import { Provider } from 'react-redux'
import { RouterProvider } from 'react-router'
import { store } from './redux/store'
import { router } from './router'

export const queryClient = new QueryClient()

export const App: FC = () => {
  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <RouterProvider router={router} />
      </QueryClientProvider>
    </Provider>
  )
}
