import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { CreateUnit, EditUnit } from './UnitForm'
import { Units } from './Units'
import { UnitsList } from './UnitsList'

export default {
  path: paths.units,
  Component: Units,
  children: [
    { path: paths.new, Component: CreateUnit },
    { path: paths.id, Component: EditUnit },
    { path: '', Component: UnitsList },
  ],
} satisfies RouteObject
