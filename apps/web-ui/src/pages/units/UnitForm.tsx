import {
  Button,
  Form,
  FormItem,
  Header,
  Input,
  RouteLink,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { api } from '@/api'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreateUnit: FC = () => {
  const companyId = useCompanyId()

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <UnitForm
      action="Create"
      title="Create a new unit"
      values={{ name: '' }}
      onSubmit={async values => {
        await api.createUnit({ body: { ...values, company_id: companyId } })
      }}
    />
  )
}

export const EditUnit: FC = () => {
  const { id } = useParams()
  const getUnit = useQuery({
    queryKey: ['unit', id],
    queryFn: () => (id ? api.getUnit({ params: { id } }) : null),
    enabled: !!id,
  })

  if (id === undefined || !getUnit.data) return null

  return (
    <UnitForm
      action="Save"
      title="Edit a unit"
      values={getUnit.data}
      onSubmit={async values => {
        await api.updateUnit({ params: { id }, body: values })
      }}
    />
  )
}

type Unit = { name: string }
type UnitFormProps = {
  action: string
  title: string
  values: Unit
  onSubmit: (values: Unit) => Promise<unknown>
}
const UnitForm: FC<UnitFormProps> = ({ action, title, values, onSubmit }) => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })

  return (
    <div>
      <nav>
        <RouteLink to={paths.units} end>
          List
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.units,
            search: searchParams.toString(),
          })
        })}
      >
        <FormItem text="Name">
          <Input type="text" autoComplete="off" {...form.register('name')} />
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
