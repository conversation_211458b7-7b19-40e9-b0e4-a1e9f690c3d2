import { ListTable, RouteLinks, type Column, Identity } from '@/components'
import { Header, RouteLink, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { api } from '@/api'
import { type ElementOf } from '@/types'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'
import { type GetUnitsFetcher } from '@cloudretail/api'

type Unit = ElementOf<Awaited<ReturnType<GetUnitsFetcher>>>

const columns: Column<Unit>[] = [
  { title: 'ID', cell: row => <Identity id={row.id} /> },
  { title: 'Name', cell: row => <RouteLink to={row.id}>{row.name}</RouteLink> },
]

export const UnitsList: FC = () => {
  const companyId = useCompanyId()
  const getUnits = useQuery({
    queryKey: ['units', companyId],
    queryFn: () =>
      companyId ? api.getUnits({ query: { company_id: companyId } }) : null,
    enabled: !!companyId,
  })

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <Header>Units</Header>
      <RouteLinks links={[{ to: paths.new, label: 'New' }]} />
      <ListTable rows={getUnits.data || []} columns={columns} />
    </div>
  )
}
