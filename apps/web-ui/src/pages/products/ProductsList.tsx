import { ListTable, RouteLinks, type Column, Identity } from '@/components'
import { Header, RouteLink, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { api } from '@/api'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'

type Product = {
  base_unit_id: string | null
  base_unit_name: string | null
  company_id: string
  created_at: string
  id: string
  name: string
}

const columns: Column<Product>[] = [
  { title: 'ID', cell: row => <Identity id={row.id} /> },
  { title: 'Name', cell: row => <RouteLink to={row.id}>{row.name}</RouteLink> },
  { title: 'Unit', cell: row => row.base_unit_name || 'No base unit' },
  {
    title: 'Actions',
    cell: row => (
      <div style={{ display: 'flex', gap: '8px' }}>
        <RouteLink to={`${row.id}/units`}>Manage Units</RouteLink>
      </div>
    ),
  },
]

export const ProductsList: FC = () => {
  const companyId = useCompanyId()

  const getProducts = useQuery({
    queryKey: ['products', companyId],
    queryFn: () => api.getProducts({ query: { company_id: companyId! } }),
    enabled: companyId !== null,
  })

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <Header>Products</Header>
      <RouteLinks links={[{ to: paths.new, label: 'New' }]} />
      <ListTable rows={getProducts.data} columns={columns} />
    </div>
  )
}
