import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { CreateProduct, EditProduct } from './ProductForm'
import { Products } from './Products'
import { ProductsList } from './ProductsList'
import { ProductUnits } from './ProductUnits'

export default {
  path: paths.products,
  Component: Products,
  children: [
    { path: paths.new, Component: CreateProduct },
    { path: `${paths.id}/units`, Component: ProductUnits },
    { path: paths.id, Component: EditProduct },
    { path: '', Component: ProductsList },
  ],
} satisfies RouteObject
