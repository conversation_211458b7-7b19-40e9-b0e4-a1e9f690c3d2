import {
  Button,
  Form,
  FormItem,
  Header,
  Input,
  RouteLink,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { api } from '@/api'
import { type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useSearchParams } from 'react-router'

export const AuthorizeUserForm: FC = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values: { email: '' } })
  const companyId = useCompanyId()

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.authorizedUsers} end>
          List
        </RouteLink>
      </nav>
      <Header>Authorize a user</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await api.updateUserAuthorize({
            body: { ...values, company_id: companyId },
          })
          await navigate({
            pathname: paths.authorizedUsers,
            search: searchParams.toString(),
          })
        })}
      >
        <FormItem text="Email">
          <Input type="email" autoComplete="off" {...form.register('email')} />
        </FormItem>
        <Button type="submit" variant="primary">
          Authorize
        </Button>
      </Form>
    </div>
  )
}
