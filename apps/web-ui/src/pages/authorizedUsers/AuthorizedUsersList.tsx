import { ListTable, RouteLinks, type Column, Identity } from '@/components'
import { Header, RouteLink, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { api } from '@/api'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'

type AuthorizedUser = { email: string; id: string; is_active: boolean }

const columns: Column<AuthorizedUser>[] = [
  { title: 'ID', cell: row => <Identity id={row.id} /> },
  {
    title: 'Email',
    cell: row => <RouteLink to={row.id}>{row.email}</RouteLink>,
  },
]

export const AuthorizedUsersList: FC = () => {
  const companyId = useCompanyId()

  const getAuthorizedUsers = useQuery({
    queryKey: ['authorizedUsers', companyId],
    queryFn: () => api.getUsersAuthorized({ query: { company_id: companyId } }),
    enabled: companyId !== null,
  })

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <Header>Authorized users</Header>
      <RouteLinks links={[{ to: paths.new, label: 'New' }]} />
      <ListTable rows={getAuthorizedUsers.data} columns={columns} />
    </div>
  )
}
