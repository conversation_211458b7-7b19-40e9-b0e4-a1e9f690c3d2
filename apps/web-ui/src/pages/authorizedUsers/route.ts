import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { AuthorizeUserForm } from './AuthorizeUserForm'
import { AuthorizedUsers } from './AuthorizedUsers'
import { AuthorizedUsersList } from './AuthorizedUsersList'

export default {
  path: paths.authorizedUsers,
  Component: AuthorizedUsers,
  children: [
    { path: paths.new, Component: AuthorizeUserForm },
    { path: '', Component: AuthorizedUsersList },
  ],
} satisfies RouteObject
