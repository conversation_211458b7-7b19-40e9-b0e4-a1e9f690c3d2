import { Layout } from '@/components'
import { paths, TOKEN_KEY } from '@/constants'
import { authSlice } from '@/redux/slices/authSlice'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { api } from '@/api'
import { useEffect, type FC } from 'react'
import { shallowEqual } from 'react-redux'
import { Outlet, useNavigate } from 'react-router'
import * as remeda from 'remeda'

export const Dashboard: FC = () => {
  const navigate = useNavigate()
  const { isRestoreTriggered, isRestoring } = useAppSelector(
    state => remeda.pick(state.auth, ['isRestoreTriggered', 'isRestoring']),
    shallowEqual,
  )
  const dispatch = useAppDispatch()

  useEffect(() => {
    ;(async () => {
      dispatch(authSlice.actions.setIsRestoreTriggered())
      const tokenId = window.localStorage.getItem(TOKEN_KEY)
      if (tokenId === null) throw new Error('No token')
      dispatch(authSlice.actions.setIsRestoring(true))
      try {
        const token = await api.getToken({ params: { id: tokenId } })
        if (token) {
          dispatch(
            authSlice.actions.setUserInfo({
              tokenId: token.id,
              email: token.user.email,
            }),
          )
        }
      } catch (error) {
        if (error instanceof Error && error.message === 'Unauthorized') {
          window.localStorage.removeItem(TOKEN_KEY)
        }
        throw error
      } finally {
        dispatch(authSlice.actions.setIsRestoring(false))
      }
    })().catch(async () => {
      await navigate(paths.login)
    })
  }, [dispatch, navigate])

  if (!isRestoreTriggered || isRestoring) return null

  return (
    <Layout>
      <Outlet />
    </Layout>
  )
}
