import { Button, Form, FormItem, Input, Typography } from '@/components/core'
import { paths, TOKEN_KEY } from '@/constants'
import { authSlice } from '@/redux/slices/authSlice'
import { useAppDispatch } from '@/redux/store'
import { api } from '@/api'
import { useState, type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router'

const values = { email: '', password: '' }

export const Login: FC = () => {
  const [error, setError] = useState('')
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const form = useForm({ values })

  return (
    <Form
      onSubmit={form.handleSubmit(async values => {
        try {
          const token = await api.createToken({ body: values })
          if (token) {
            dispatch(
              authSlice.actions.setUserInfo({
                tokenId: token.id,
                email: token.user.email,
              }),
            )
            window.localStorage.setItem(TOKEN_KEY, token.id)
            await navigate(paths.orders)
          }
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Unknown error')
        }
      })}
    >
      <FormItem text="Email">
        <Input type="email" autoComplete="off" {...form.register('email')} />
      </FormItem>
      <FormItem text="Password">
        <Input
          type="password"
          autoComplete="off"
          {...form.register('password')}
        />
      </FormItem>
      {error && <Typography>{error}</Typography>}
      <Button type="submit" variant="primary">
        Login
      </Button>
    </Form>
  )
}
