import { Button, Form, FormItem, Input } from '@/components/core'
import { paths } from '@/constants'
import { api } from '@/api'
import { type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router'

const values = { email: '', password: '' }

export const Register: FC = () => {
  const navigate = useNavigate()
  const form = useForm({ values })

  return (
    <Form
      onSubmit={form.handleSubmit(async values => {
        await api.registerUser({ body: values })
        await navigate(paths.login)
      })}
    >
      <FormItem text="Email">
        <Input type="email" autoComplete="off" {...form.register('email')} />
      </FormItem>
      <FormItem text="Password">
        <Input
          type="password"
          autoComplete="off"
          {...form.register('password')}
        />
      </FormItem>
      <Button type="submit" variant="primary">
        Register
      </Button>
    </Form>
  )
}
