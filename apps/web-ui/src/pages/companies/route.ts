import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { Companies } from './Companies'
import { CompaniesList } from './CompaniesList'
import { CreateCompany, EditCompany } from './CompanyForm'

export default {
  path: paths.companies,
  Component: Companies,
  children: [
    { path: paths.new, Component: CreateCompany },
    { path: paths.id, Component: EditCompany },
    { path: '', Component: CompaniesList },
  ],
} satisfies RouteObject
