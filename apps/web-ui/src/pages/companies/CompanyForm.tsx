import { api } from '@/api'
import {
  Button,
  Form,
  FormItem,
  Header,
  Input,
  RouteLink,
} from '@/components/core'
import { paths } from '@/constants'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreateCompany: FC = () => {
  return (
    <CompanyForm
      action="Create"
      title="Create a new company"
      values={{ name: '' }}
      onSubmit={async values => {
        await api.createCompany({ body: values })
      }}
    />
  )
}

export const EditCompany: FC = () => {
  const { id } = useParams()
  const getCompany = useQuery({
    queryKey: ['company', id],
    queryFn: () => api.getCompany({ params: { id: id! } }),
    enabled: id !== undefined,
  })

  if (id === undefined || getCompany.data === undefined) return null

  return (
    <CompanyForm
      action="Save"
      title="Edit a company"
      values={getCompany.data}
      onSubmit={async values => {
        await api.updateCompany({ params: { id }, body: values })
      }}
    />
  )
}

type Company = { name: string }
type CompanyFormProps = {
  action: string
  title: string
  values: Company
  onSubmit: (values: Company) => Promise<unknown>
}
const CompanyForm: FC<CompanyFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })

  return (
    <div>
      <nav>
        <RouteLink to={paths.companies} end>
          List
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.companies,
            search: searchParams.toString(),
          })
        })}
      >
        <FormItem text="Name">
          <Input type="text" autoComplete="off" {...form.register('name')} />
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
