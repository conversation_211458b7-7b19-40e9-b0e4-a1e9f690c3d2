import { api } from '@/api'
import { ListTable, RouteLinks, type Column, Identity } from '@/components'
import { Header, RouteLink } from '@/components/core'
import { paths } from '@/constants'
import { type ElementOf } from '@/types'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'

type Company = ElementOf<Awaited<ReturnType<typeof api.getCompanies>>>

const columns: Column<Company>[] = [
  { title: 'ID', cell: row => <Identity id={row.id} /> },
  { title: 'Name', cell: row => <RouteLink to={row.id}>{row.name}</RouteLink> },
]

export const CompaniesList: FC = () => {
  const getCompanies = useQuery({
    queryKey: ['companies'],
    queryFn: api.getCompanies,
  })

  return (
    <div>
      <Header>Companies</Header>
      <RouteLinks links={[{ to: paths.new, label: 'New' }]} />
      <ListTable rows={getCompanies.data} columns={columns} />
    </div>
  )
}
