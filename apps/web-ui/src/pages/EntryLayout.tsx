import { List, ListItem, RouteLink } from '@/components/core'
import { paths } from '@/constants'
import { css } from '@/styled-system/css'
import { type FC } from 'react'
import { Outlet } from 'react-router'

const links = [
  { label: 'Login', path: paths.login },
  { label: 'Register', path: paths.register },
]

export const EntryLayout: FC = () => {
  return (
    <div
      className={css({
        display: 'flex',
        flexDirection: 'column',
        gap: '16px',
        paddingBlockStart: '32px',
        marginInline: 'auto',
        maxWidth: '320px',
      })}
    >
      <List className={css({ display: 'flex', gap: '4px' })}>
        {links.map(({ label, path }) => (
          <ListItem key={path}>
            <RouteLink to={path}>{label}</RouteLink>
          </ListItem>
        ))}
      </List>
      <Outlet />
    </div>
  )
}
