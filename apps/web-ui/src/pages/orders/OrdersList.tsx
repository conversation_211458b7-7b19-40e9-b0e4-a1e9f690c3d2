import { api } from '@/api'
import { ListTable, RouteLinks, type Column, Identity } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { type ElementOf } from '@/types'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'

type Order = ElementOf<Awaited<ReturnType<typeof api.getOrders>>>

const columns: Column<Order>[] = [
  { title: 'ID', cell: row => <Identity id={row.id} /> },
  { title: 'Customer', cell: row => row.customer_name },
  { title: 'Total', cell: row => `$${row.total_amount}` },
  { title: 'Date', cell: row => new Date(row.created_at).toLocaleDateString() },
]

export const OrdersList: FC = () => {
  const companyId = useCompanyId()
  const getOrders = useQuery({
    queryKey: ['orders', companyId],
    queryFn: () => api.getOrders({ query: { company_id: companyId } }),
    enabled: companyId !== null,
  })

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <Header>Orders</Header>
      <RouteLinks links={[{ to: paths.new, label: 'New' }]} />
      <ListTable rows={getOrders.data || []} columns={columns} />
    </div>
  )
}
