import { api } from '@/api'
import {
  Button,
  Form,
  FormItem,
  Header,
  RouteLink,
  Select,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreateOrder: FC = () => {
  const companyId = useCompanyId()

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <OrderForm
      action="Create"
      title="Create a new order"
      values={{ customer_id: '' }}
      onSubmit={async values => {
        await api.createOrder({
          body: { ...values, company_id: companyId, items: [] },
        })
      }}
    />
  )
}

export const EditOrder: FC = () => {
  const { id } = useParams()
  const getOrder = useQuery({
    queryKey: ['order', id],
    queryFn: () => api.getOrder({ params: { id: id! } }),
    enabled: id !== undefined,
  })

  if (id === undefined || getOrder.data === undefined) return null

  return (
    <OrderForm
      action="Save"
      title="Edit an order"
      values={{ customer_id: getOrder.data.customer_id }}
      onSubmit={async values => {
        await api.updateOrder({ params: { id }, body: values })
      }}
    />
  )
}

type Order = { customer_id: string }

type OrderFormProps = {
  action: string
  title: string
  values: Order
  onSubmit: (values: Order) => Promise<unknown>
}

const OrderForm: FC<OrderFormProps> = ({ action, title, values, onSubmit }) => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })
  const companyId = useCompanyId()

  const getCustomers = useQuery({
    queryKey: ['customers', companyId],
    queryFn: () => api.getCustomers({ query: { company_id: companyId! } }),
    enabled: companyId !== null,
  })

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.orders} end>
          List
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.orders,
            search: searchParams.toString(),
          })
        })}
      >
        <FormItem text="Customer">
          <Select {...form.register('customer_id')}>
            <option value="">Select a customer</option>
            {getCustomers.data?.map(customer => (
              <option key={customer.id} value={customer.id}>
                {customer.name}
              </option>
            ))}
          </Select>
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
