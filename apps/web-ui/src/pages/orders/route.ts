import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { Create<PERSON>rder, EditOrder } from './OrderForm'
import { Orders } from './Orders'
import { OrdersList } from './OrdersList'

export default {
  path: paths.orders,
  Component: Orders,
  children: [
    { path: paths.new, Component: CreateOrder },
    { path: paths.id, Component: EditOrder },
    { path: '', Component: OrdersList },
  ],
} satisfies RouteObject
