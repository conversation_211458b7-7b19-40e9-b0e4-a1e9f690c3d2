import { api } from '@/api'
import {
  Button,
  Form,
  FormItem,
  Header,
  Input,
  RouteLink,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreateCustomer: FC = () => {
  const companyId = useCompanyId()

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <CustomerForm
      action="Create"
      title="Create a new customer"
      values={{ name: '' }}
      onSubmit={async values => {
        await api.createCustomer({ body: { ...values, company_id: companyId } })
      }}
    />
  )
}

export const EditCustomer: FC = () => {
  const { id } = useParams()
  const getCustomer = useQuery({
    queryKey: ['customer', id],
    queryFn: () => api.getCustomer({ params: { id: id! } }),
    enabled: id !== undefined,
  })

  if (id === undefined || getCustomer.data === undefined) return null

  return (
    <CustomerForm
      action="Save"
      title="Edit a customer"
      values={getCustomer.data}
      onSubmit={async values => {
        await api.updateCustomer({ params: { id }, body: values })
      }}
    />
  )
}

type Customer = { name: string }
type CustomerFormProps = {
  action: string
  title: string
  values: Customer
  onSubmit: (values: Customer) => Promise<unknown>
}
const CustomerForm: FC<CustomerFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })
  const companyId = useCompanyId()

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.customers} end>
          List
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.customers,
            search: searchParams.toString(),
          })
        })}
      >
        <FormItem text="Name">
          <Input type="text" autoComplete="off" {...form.register('name')} />
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
