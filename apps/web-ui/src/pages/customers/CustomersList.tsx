import { api } from '@/api'
import { ListTable, RouteLinks, type Column, Identity } from '@/components'
import { Header, RouteLink, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { type ElementOf } from '@/types'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'

type Customer = ElementOf<Awaited<ReturnType<typeof api.getCustomers>>>

const columns: Column<Customer>[] = [
  { title: 'ID', cell: row => <Identity id={row.id} /> },
  { title: 'Name', cell: row => <RouteLink to={row.id}>{row.name}</RouteLink> },
]

export const CustomersList: FC = () => {
  const companyId = useCompanyId()
  const getCustomers = useQuery({
    queryKey: ['customers', companyId],
    queryFn: () => api.getCustomers({ query: { company_id: companyId } }),
    enabled: companyId !== null,
  })

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <Header>Customers</Header>
      <RouteLinks links={[{ to: paths.new, label: 'New' }]} />
      <ListTable rows={getCustomers.data || []} columns={columns} />
    </div>
  )
}
