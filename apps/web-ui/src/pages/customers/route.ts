import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, EditCustomer } from './CustomerForm'
import { Customers } from './Customers'
import { CustomersList } from './CustomersList'

export default {
  path: paths.customers,
  Component: Customers,
  children: [
    { path: paths.new, Component: CreateCustomer },
    { path: paths.id, Component: EditCustomer },
    { path: '', Component: CustomersList },
  ],
} satisfies RouteObject
