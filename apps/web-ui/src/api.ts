import {
  type ActivateUserFetcher,
  type AddProductUnitFetcher,
  type AuthorizeUserFetcher,
  type CreateCompanyFetcher,
  type CreateCustomerFetcher,
  type CreateOrderFetcher,
  type CreateProductFetcher,
  type CreateTokenFetcher,
  type CreateUnitFetcher,
  type DeleteCompanyFetcher,
  type DeleteCustomerFetcher,
  type DeleteOrderFetcher,
  type DeleteTokenFetcher,
  type GetAuthorizedUsersFetcher,
  type GetCompaniesFetcher,
  type GetCompanyFetcher,
  type GetCustomerFetcher,
  type GetCustomersFetcher,
  type GetOrderFetcher,
  type GetOrdersFetcher,
  type GetProductFetcher,
  type GetProductUnitsFetcher,
  type GetProductsFetcher,
  type GetTokenFetcher,
  type GetUnitFetcher,
  type GetUnitsFetcher,
  type RegisterUserFetcher,
  type RemoveProductUnitFetcher,
  type SetProductBaseUnitFetcher,
  type UpdateCompanyFetcher,
  type Update<PERSON>ustomerFetcher,
  type Update<PERSON>rderFetcher,
  type UpdateProductFetcher,
  type UpdateUnitFetcher,
  endpoints,
} from '@cloudretail/api'
import { createFetcher } from './createFetcher'

export const api = {
  activateUser: createFetcher<ActivateUserFetcher>(endpoints.activateUser),
  addProductUnit: createFetcher<AddProductUnitFetcher>(
    endpoints.addProductUnit,
  ),
  createCompany: createFetcher<CreateCompanyFetcher>(endpoints.createCompany),
  createCustomer: createFetcher<CreateCustomerFetcher>(
    endpoints.createCustomer,
  ),
  createOrder: createFetcher<CreateOrderFetcher>(endpoints.createOrder),
  createProduct: createFetcher<CreateProductFetcher>(endpoints.createProduct),
  createToken: createFetcher<CreateTokenFetcher>(endpoints.createToken),
  createUnit: createFetcher<CreateUnitFetcher>(endpoints.createUnit),
  deleteCompany: createFetcher<DeleteCompanyFetcher>(endpoints.deleteCompany),
  deleteCustomer: createFetcher<DeleteCustomerFetcher>(
    endpoints.deleteCustomer,
  ),
  deleteOrder: createFetcher<DeleteOrderFetcher>(endpoints.deleteOrder),
  deleteToken: createFetcher<DeleteTokenFetcher>(endpoints.deleteToken),
  getCompanies: createFetcher<GetCompaniesFetcher>(endpoints.getCompanies),
  getCompany: createFetcher<GetCompanyFetcher>(endpoints.getCompany),
  getCustomer: createFetcher<GetCustomerFetcher>(endpoints.getCustomer),
  getCustomers: createFetcher<GetCustomersFetcher>(endpoints.getCustomers),
  getOrder: createFetcher<GetOrderFetcher>(endpoints.getOrder),
  getOrders: createFetcher<GetOrdersFetcher>(endpoints.getOrders),
  getProduct: createFetcher<GetProductFetcher>(endpoints.getProduct),
  getProductUnits: createFetcher<GetProductUnitsFetcher>(
    endpoints.getProductUnits,
  ),
  getProducts: createFetcher<GetProductsFetcher>(endpoints.getProducts),
  getToken: createFetcher<GetTokenFetcher>(endpoints.getToken),
  getUsersAuthorized: createFetcher<GetAuthorizedUsersFetcher>(
    endpoints.getUsersAuthorized,
  ),
  getUnit: createFetcher<GetUnitFetcher>(endpoints.getUnit),
  getUnits: createFetcher<GetUnitsFetcher>(endpoints.getUnits),
  registerUser: createFetcher<RegisterUserFetcher>(endpoints.registerUser),
  removeProductUnit: createFetcher<RemoveProductUnitFetcher>(
    endpoints.removeProductUnit,
  ),
  setProductBaseUnit: createFetcher<SetProductBaseUnitFetcher>(
    endpoints.setProductBaseUnit,
  ),
  updateCompany: createFetcher<UpdateCompanyFetcher>(endpoints.updateCompany),
  updateCustomer: createFetcher<UpdateCustomerFetcher>(
    endpoints.updateCustomer,
  ),
  updateOrder: createFetcher<UpdateOrderFetcher>(endpoints.updateOrder),
  updateProduct: createFetcher<UpdateProductFetcher>(endpoints.updateProduct),

  updateUnit: createFetcher<UpdateUnitFetcher>(endpoints.updateUnit),
  updateUserAuthorize: createFetcher<AuthorizeUserFetcher>(
    endpoints.updateUserAuthorize,
  ),
}
