import { css } from '@/styled-system/css'
import { type FC } from 'react'
import { NavLink, useSearchParams, type NavLinkProps } from 'react-router'

type RouteLinkProps = Omit<NavLinkProps, 'to'> & { to: string }
export const RouteLink: FC<RouteLinkProps> = ({ to, ...props }) => {
  const [searchParams] = useSearchParams()

  return (
    <NavLink
      className={({ isActive }) =>
        css(isActive ? {} : { textDecoration: 'none' })
      }
      to={{ pathname: to, search: searchParams.toString() }}
      {...props}
    />
  )
}
