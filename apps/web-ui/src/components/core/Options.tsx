import { type FC } from 'react'

type Item = { id: string; name: string }

const notSelected: Item = { id: '', name: 'Not selected' }

type OptionsProps = { items: Item[] | undefined }
export const Options: FC<OptionsProps> = ({ items }) => {
  if (items === undefined || items.length === 0) return null

  return [notSelected, ...items].map(item => (
    <option key={item.id} value={item.id}>
      {item.name}
    </option>
  ))
}
