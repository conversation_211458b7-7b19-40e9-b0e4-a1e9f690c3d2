import { styled } from '@/styled-system/jsx'

export const Button = styled('button', {
  base: {
    padding: '4px 8px',
    border: '1px solid',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: 'inherit',
    fontFamily: 'inherit',
  },
  variants: {
    variant: {
      default: {
        backgroundColor: 'white',
        borderColor: 'gray',
        '&:hover': { backgroundColor: '#f5f5f5' },
      },
      primary: {
        backgroundColor: 'black',
        borderColor: 'black',
        color: 'white',
        '&:hover': { backgroundColor: '#333' },
      },
      link: {
        border: 'none',
        backgroundColor: 'transparent',
        textDecoration: 'underline',
        '&:hover': { textDecoration: 'none' },
      },
    },
  },
  defaultVariants: { variant: 'default' },
})
