import { css } from '@/styled-system/css'
import { type FC } from 'react'
import { CompanySelect, LogoutButton, UserInfo } from '.'
import { BuildInfo } from './BuildInfo'

export const Navbar: FC = () => {
  return (
    <div className={css({ display: 'flex', justifyContent: 'space-between' })}>
      <CompanySelect />
      <div className={css({ display: 'flex' })}>
        <BuildInfo />
        <UserInfo />
        <LogoutButton />
      </div>
    </div>
  )
}
