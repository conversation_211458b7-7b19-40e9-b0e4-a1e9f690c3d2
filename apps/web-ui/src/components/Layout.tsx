import { css } from '@/styled-system/css'
import { type FC, type PropsWithChildren } from 'react'
import { Navbar, Sidebar } from '.'

export const Layout: FC<PropsWithChildren> = ({ children }) => {
  return (
    <div
      className={css({
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        height: '100%',
      })}
    >
      <Navbar />
      <div className={css({ display: 'flex', gap: '8px', flexGrow: '1' })}>
        <div className={css({ padding: '8px', border: '1px solid lightgrey' })}>
          <Sidebar />
        </div>
        <div
          className={css({
            flexGrow: '1',
            padding: '8px',
            border: '1px solid lightgrey',
          })}
        >
          {children}
        </div>
      </div>
    </div>
  )
}
