import { api } from '@/api'
import { useQuery } from '@tanstack/react-query'
import { Dialog, useDialog } from './Dialog'
import { ProductSelectTable } from './ProductSelectTable'
import { Button, Label } from './core'

type ProductSelectProps = { value: string; onChange: (value: string) => void }
export function ProductSelect({ value, onChange }: ProductSelectProps) {
  const productSelectDialog = useDialog({ initialOpen: false })

  const getProduct = useQuery({
    queryKey: ['product', value],
    queryFn: () => api.getProduct({ params: { id: value } }),
    enabled: !!value,
  })

  return (
    <div>
      <Label>Product</Label>
      <div>
        <span>{getProduct.data?.name}</span>
        {value && (
          <div>
            <button
              type="button"
              onClick={() => {
                onChange('')
              }}
            >
              x
            </button>
          </div>
        )}
      </div>
      <Button type="button" {...productSelectDialog.buttonProps}>
        ...
      </Button>
      <Dialog {...productSelectDialog.dialogProps}>
        <ProductSelectTable
          onSelect={product => {
            onChange(product.id)
            productSelectDialog.onOpenChange(false)
          }}
        />
      </Dialog>
    </div>
  )
}
