import { api } from '@/api'
import { useAppSelector } from '@/redux/store'
import { useQuery } from '@tanstack/react-query'
import { type ChangeEvent, type FC } from 'react'
import { useSearchParams } from 'react-router'
import { Options, Select } from './core'

export const CompanySelect: FC = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const isAuthenticated = useAppSelector(state => state.auth.isAuthenticated)
  const companies = useQuery({
    queryKey: ['companies'],
    queryFn: api.getCompanies,
    enabled: isAuthenticated,
  })

  if (companies.data === undefined) return null

  return (
    <Select
      value={searchParams.get('company_id') ?? ''}
      onChange={({ target: { value } }: ChangeEvent<HTMLSelectElement>) => {
        setSearchParams(value ? { company_id: value } : {}, { replace: true })
      }}
    >
      <Options items={companies.data} />
    </Select>
  )
}
