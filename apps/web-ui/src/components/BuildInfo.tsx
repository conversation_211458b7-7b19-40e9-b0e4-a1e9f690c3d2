import { DATETIME_FORMAT } from '@/constants'
import { css } from '@/styled-system/css'
import { formatInTimeZone } from 'date-fns-tz'
import { type ComponentType } from 'react'

export const BuildInfo: ComponentType = () => {
  return (
    <ul
      className={css({
        display: 'flex',
        listStyleType: 'none',
        marginBlock: 0,
        gap: '0.5rem',
      })}
    >
      <li className={css({ display: 'flex', gap: '0.25rem' })}>
        <span>Commit:</span>
        <b>{import.meta.env.VITE_APP_BUILD_HASH}</b>
      </li>
      <li className={css({ display: 'flex', gap: '0.25rem' })}>
        <span>Date:</span>
        <b>
          {formatInTimeZone(
            import.meta.env.VITE_APP_BUILD_TIMESTAMP,
            'UTC',
            DATETIME_FORMAT,
          )}
        </b>
      </li>
    </ul>
  )
}
