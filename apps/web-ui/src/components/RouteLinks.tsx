import { type FC } from 'react'
import { List, ListItem, RouteLink } from './core'

type RouteLinksProps = { links: { to: string; label: string }[] }
export const RouteLinks: FC<RouteLinksProps> = ({ links }) => {
  return (
    <List>
      {links.map(({ to, label }) => (
        <ListItem key={to}>
          <RouteLink to={to} end>
            {label}
          </RouteLink>
        </ListItem>
      ))}
    </List>
  )
}
