import {
  FloatingFocusManager,
  FloatingOverlay,
  FloatingPortal,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
} from '@floating-ui/react'
import { useState, type FC, type PropsWithChildren } from 'react'

type UseDialogInput = { initialOpen: boolean }
export const useDialog = ({ initialOpen }: UseDialogInput) => {
  const [open, onOpenChange] = useState(initialOpen)
  const {
    context,
    refs: { setFloating, setReference },
  } = useFloating({ open, onOpenChange })
  const click = useClick(context)
  const role = useRole(context)
  const dismiss = useDismiss(context, { outsidePressEvent: 'mousedown' })
  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    role,
    dismiss,
  ])
  const referenceProps = getReferenceProps()
  const floatingProps = getFloatingProps()

  return {
    onOpenChange,
    buttonProps: { ref: setReference, ...referenceProps },
    dialogProps: { open, context, floatingProps, setFloating },
  }
}

type UseDialogReturn = ReturnType<typeof useDialog>

type DialogProps = UseDialogReturn['dialogProps']
export const Dialog: FC<PropsWithChildren<DialogProps>> = ({
  children,
  open,
  context,
  floatingProps,
  setFloating,
}) => {
  return (
    <FloatingPortal>
      {open && (
        <FloatingOverlay lockScroll>
          <FloatingFocusManager context={context}>
            <div ref={setFloating} {...floatingProps}>
              {children}
            </div>
          </FloatingFocusManager>
        </FloatingOverlay>
      )}
    </FloatingPortal>
  )
}
