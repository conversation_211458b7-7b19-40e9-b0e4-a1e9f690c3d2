import { paths, TOKEN_KEY } from '@/constants'
import { authSlice } from '@/redux/slices/authSlice'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { api } from '@/api'
import { type FC } from 'react'
import { useNavigate } from 'react-router'
import { Button } from './core'

export const LogoutButton: FC = () => {
  const navigate = useNavigate()
  const tokenId = useAppSelector(state => state.auth.tokenId)
  const dispatch = useAppDispatch()

  return (
    <Button
      type="submit"
      variant="link"
      onClick={async () => {
        if (tokenId === null) return
        await api.deleteToken({ params: { id: tokenId } })
        window.localStorage.removeItem(TOKEN_KEY)
        dispatch(authSlice.actions.reset())
        await navigate(paths.login)
      }}
    >
      log out
    </Button>
  )
}
