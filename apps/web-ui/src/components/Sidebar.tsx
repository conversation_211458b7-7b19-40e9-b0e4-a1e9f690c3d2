import { paths } from '@/constants'
import { css } from '@/styled-system/css'
import { type FC } from 'react'
import { List, ListItem, RouteLink } from './core'

const links = [
  { label: 'Orders', path: paths.orders },
  { label: 'Customers', path: paths.customers },
  { label: 'Products', path: paths.products },
  { label: 'Units', path: paths.units },
  { label: 'Users', path: paths.authorizedUsers },
  { label: 'Companies', path: paths.companies },
]

export const Sidebar: FC = () => {
  return (
    <List className={css({ display: 'flex', flexDirection: 'column' })}>
      {links.map(({ label, path }) => (
        <ListItem key={path}>
          <RouteLink to={path}>{label}</RouteLink>
        </ListItem>
      ))}
    </List>
  )
}
