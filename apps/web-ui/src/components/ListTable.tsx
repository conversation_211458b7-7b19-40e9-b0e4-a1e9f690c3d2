import { css } from '@/styled-system/css'
import { type ReactNode } from 'react'
import { Table } from './core'

type Entity = { id: string }

export type Column<T extends Entity> = {
  title: string
  cell: (row: T) => ReactNode
  textAlign?: 'start' | 'center' | 'end'
}

type ListTableProps<T extends Entity> = {
  columns: Column<T>[]
  rows: T[] | undefined
  onRowSelect?: (row: T) => void
}
export function ListTable<T extends Entity>({
  columns,
  rows = [],
  onRowSelect,
}: ListTableProps<T>): ReactNode {
  return (
    <Table>
      <thead>
        <tr>
          {columns.map((column, columnIndex) => (
            <th
              key={columnIndex}
              className={css({
                border: '1px solid lightgrey',
                paddingBlock: '4px',
                paddingInline: '8px',
              })}
            >
              {column.title}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {rows.length === 0 ? (
          <tr>
            <td
              className={css({
                border: '1px solid lightgrey',
                paddingBlock: '4px',
                paddingInline: '8px',
              })}
              colSpan={columns.length}
            >
              No data
            </td>
          </tr>
        ) : (
          rows.map(row => (
            <tr
              key={row.id}
              onClick={() => {
                onRowSelect?.(row)
              }}
            >
              {columns.map((column, columnIndex) => (
                <td
                  key={columnIndex}
                  className={css({
                    border: '1px solid lightgrey',
                    paddingBlock: '4px',
                    paddingInline: '8px',
                  })}
                >
                  {column.cell(row)}
                </td>
              ))}
            </tr>
          ))
        )}
      </tbody>
    </Table>
  )
}
