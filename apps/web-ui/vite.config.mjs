import viteReact from '@vitejs/plugin-react'
import url from 'node:url'

/** @type {import('vite').UserConfig} */
export default {
  build: {
    rollupOptions: {
      output: {
        manualChunks: id => {
          if (/node_modules/.test(id)) {
            return 'vendor'
          }
        },
      },
    },
  },
  clearScreen: false,
  plugins: [viteReact()],
  resolve: {
    alias: [
      {
        find: '@',
        replacement: url.fileURLToPath(new url.URL('./src', import.meta.url)),
      },
    ],
  },
  server: {
    proxy: {
      '^/api/.*': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, ''),
      },
    },
  },
}
