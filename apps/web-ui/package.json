{"name": "@cloudretail/web-ui", "version": "1.0.0", "private": true, "scripts": {"build": "npm-run-all -s prepare build:sources", "build:sources": "vite build", "lint": "DEBUG=eslint:cli-engine eslint --fix .", "prepare": "panda codegen --clean", "start": "npm-run-all -s prepare -p typecheck:w vite", "typecheck": "tsc --noEmit", "typecheck:w": "tsc --noEmit --preserveWatchOutput --watch", "vite": "vite"}, "devDependencies": {"@cloudretail/api": "*", "@eslint/js": "^9.29.0", "@floating-ui/react": "^0.27.12", "@pandacss/dev": "^0.54.0", "@pandacss/eslint-plugin": "^0.2.12", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.81.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "eslint": "^9.29.0", "npm-run-all": "^4.1.5", "path-to-regexp": "^8.2.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-redux": "^9.2.0", "react-router": "^7.6.2", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "vite": "^6.3.5"}}